"use client";

import { useAuth } from '@/context/AuthContext';
import { useCredit } from '@/context/CreditContext';
import { motion } from 'framer-motion';
import { FaSignOutAlt, FaUserCircle, FaCoins, FaChevronDown } from 'react-icons/fa';
import { TbLayoutSidebarRightCollapseFilled, TbLayoutSidebarRightExpandFilled } from "react-icons/tb";
import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function TopBar({ sidebarCollapsed, onToggleSidebar }) {
  const { user, logout } = useAuth();
  const { creditBalance, loading: creditLoading } = useCredit();
  const router = useRouter();
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleProfileClick = () => {
    setIsDropdownOpen(false);
    router.push('/profile');
  };

  const handleCreditsClick = () => {
    router.push('/profile?tab=credits');
  };

  const handleLogout = () => {
    setIsDropdownOpen(false);
    logout();
  };

  return (
    <div className="bg-white rounded-xl shadow-lg p-4 w-full">
      <div className="flex justify-between items-center gap-3">
        {/* Sidebar Toggle Button */}
        <button
          onClick={onToggleSidebar}
          className="p-3 rounded-lg hover:bg-gray-100 transition-colors text-gray-600 hover:text-gray-800"
          title={sidebarCollapsed ? "Expand sidebar" : "Collapse sidebar"}
        >
          {sidebarCollapsed ? <TbLayoutSidebarRightCollapseFilled size={37} /> : <TbLayoutSidebarRightExpandFilled size={37} />}
        </button>

        <div className="flex items-center gap-3">
        {/* Credit Balance Display */}
        <div
          onClick={handleCreditsClick}
          className="flex items-center gap-2 bg-gradient-to-r from-yellow-50 to-orange-50 px-3 py-1.5 rounded-lg border border-yellow-200 cursor-pointer hover:from-yellow-100 hover:to-orange-100 transition-colors"
        >
          <FaCoins className="text-yellow-600 text-lg" />
          <span className="font-semibold text-yellow-700 flex items-center gap-2">
            {creditBalance !== null ? (
              `${creditBalance} credits`
            ) : creditLoading ? (
              <>
                <div className="w-4 h-4 border-2 border-yellow-600 border-t-transparent rounded-full animate-spin"></div>
                <span>Loading...</span>
              </>
            ) : (
              ''
            )}
          </span>
        </div>

        {/* User Dropdown */}
        <div className="relative" ref={dropdownRef}>
          <motion.div
            className="flex items-center gap-2 cursor-pointer bg-gray-50 px-3 py-1.5 rounded-lg"
            whileHover={{
              backgroundColor: "#E6FFFA",
              transition: { duration: 0.2 }
            }}
            onClick={() => setIsDropdownOpen(!isDropdownOpen)}
          >
            <FaUserCircle className="text-teal-600 text-xl" />
            <span className="font-medium text-gray-700">{user?.first_name || 'User'}</span>
            <FaChevronDown className={`text-gray-500 text-sm transition-transform duration-200 ${isDropdownOpen ? 'rotate-180' : ''}`} />
          </motion.div>

          {/* Dropdown Menu */}
          {isDropdownOpen && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.2 }}
              className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50"
            >
              <button
                onClick={handleProfileClick}
                className="w-full text-left px-4 py-2 text-gray-700 hover:bg-gray-50 flex items-center gap-2"
              >
                <FaUserCircle className="text-gray-500" />
                Profile
              </button>
              <hr className="my-1 border-gray-200" />
              <button
                onClick={handleLogout}
                className="w-full text-left px-4 py-2 text-red-600 hover:bg-red-50 flex items-center gap-2"
              >
                <FaSignOutAlt className="text-red-500" />
                Logout
              </button>
            </motion.div>
          )}
        </div>
        </div>
      </div>
    </div>
  );
}